# Source: @DeepCharts Youtube Channel (https://www.youtube.com/@DeepCharts)

import streamlit as st
import plotly.express as px
import plotly.graph_objects as go
import pandas as pd
import yfinance as yf
from datetime import datetime, timedelta
import pytz
import ta
import numpy as np
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import PolynomialFeatures
from sklearn.metrics import mean_squared_error, r2_score
import warnings
warnings.filterwarnings('ignore')

##########################################################################################
## PART 1: Define Functions for Pulling, Processing, and Creating Techincial Indicators ##
##########################################################################################

# Fetch stock data based on the ticker, period, and interval
def fetch_stock_data(ticker, period, interval):
    end_date = datetime.now()
    if period == '1wk':
        start_date = end_date - timedelta(days=7)
        data = yf.download(ticker, start=start_date, end=end_date, interval=interval)
    else:
        data = yf.download(ticker, period=period, interval=interval)
    return data

# Process data to ensure it is timezone-aware and has the correct format
def process_data(data):
    if data.index.tzinfo is None:
        data.index = data.index.tz_localize('UTC')
    data.index = data.index.tz_convert('US/Eastern')
    data.reset_index(inplace=True)
    data.rename(columns={'Date': 'Datetime'}, inplace=True)
    return data

# Calculate basic metrics from the stock data
def calculate_metrics(data):
    last_close = float(data['Close'].iloc[-1])
    prev_close = float(data['Close'].iloc[0])
    change = last_close - prev_close
    pct_change = (change / prev_close) * 100
    high = float(data['High'].max())
    low = float(data['Low'].min())
    volume = int(data['Volume'].sum())
    return last_close, change, pct_change, high, low, volume

# Add simple moving average (SMA) and exponential moving average (EMA) indicators
def add_technical_indicators(data):
    try:
        # Calculate SMA and EMA manually to avoid dimension issues
        data['SMA_20'] = data['Close'].rolling(window=20).mean()

        # Calculate EMA manually
        data['EMA_20'] = data['Close'].ewm(span=20).mean()

        return data
    except Exception as e:
        print(f"Error in technical indicators: {e}")
        # Fallback: add empty columns
        data['SMA_20'] = None
        data['EMA_20'] = None
        return data

# Prediction Functions
def linear_regression_prediction(data, days_ahead=30):
    """Simple linear regression prediction"""
    try:
        # Prepare data
        data_clean = data.dropna()
        X = np.arange(len(data_clean)).reshape(-1, 1)
        y = data_clean['Close'].values

        # Train model
        model = LinearRegression()
        model.fit(X, y)

        # Predict future
        future_X = np.arange(len(data_clean), len(data_clean) + days_ahead).reshape(-1, 1)
        future_prices = model.predict(future_X)

        # Create future dates
        last_date = data_clean['Datetime'].iloc[-1]
        future_dates = pd.date_range(start=last_date + timedelta(days=1), periods=days_ahead, freq='D')

        return future_dates, future_prices, model.score(X, y)
    except Exception as e:
        st.error(f"Linear regression error: {e}")
        return None, None, None

def polynomial_regression_prediction(data, days_ahead=30, degree=2):
    """Polynomial regression prediction"""
    try:
        # Prepare data
        data_clean = data.dropna()
        X = np.arange(len(data_clean)).reshape(-1, 1)
        y = data_clean['Close'].values

        # Create polynomial features
        poly_features = PolynomialFeatures(degree=degree)
        X_poly = poly_features.fit_transform(X)

        # Train model
        model = LinearRegression()
        model.fit(X_poly, y)

        # Predict future
        future_X = np.arange(len(data_clean), len(data_clean) + days_ahead).reshape(-1, 1)
        future_X_poly = poly_features.transform(future_X)
        future_prices = model.predict(future_X_poly)

        # Create future dates
        last_date = data_clean['Datetime'].iloc[-1]
        future_dates = pd.date_range(start=last_date + timedelta(days=1), periods=days_ahead, freq='D')

        return future_dates, future_prices, model.score(X_poly, y)
    except Exception as e:
        st.error(f"Polynomial regression error: {e}")
        return None, None, None

def moving_average_prediction(data, days_ahead=30, window=20):
    """Moving average trend prediction"""
    try:
        data_clean = data.dropna()

        # Calculate trend from moving average
        ma = data_clean['Close'].rolling(window=window).mean()
        trend = ma.iloc[-1] - ma.iloc[-window]
        daily_trend = trend / window

        # Project trend forward
        future_prices = []
        last_price = data_clean['Close'].iloc[-1]

        for i in range(days_ahead):
            future_price = last_price + (daily_trend * (i + 1))
            future_prices.append(future_price)

        # Create future dates
        last_date = data_clean['Datetime'].iloc[-1]
        future_dates = pd.date_range(start=last_date + timedelta(days=1), periods=days_ahead, freq='D')

        return future_dates, future_prices, None
    except Exception as e:
        st.error(f"Moving average prediction error: {e}")
        return None, None, None

def exponential_smoothing_prediction(data, days_ahead=30, alpha=0.3):
    """Exponential smoothing prediction"""
    try:
        data_clean = data.dropna()
        prices = data_clean['Close'].values

        # Calculate exponential smoothing
        smoothed = [prices[0]]
        for i in range(1, len(prices)):
            smoothed.append(alpha * prices[i] + (1 - alpha) * smoothed[i-1])

        # Calculate trend
        trend = smoothed[-1] - smoothed[-10] if len(smoothed) > 10 else 0
        daily_trend = trend / 10

        # Project forward
        future_prices = []
        last_smoothed = smoothed[-1]

        for i in range(days_ahead):
            future_price = last_smoothed + (daily_trend * (i + 1))
            future_prices.append(future_price)

        # Create future dates
        last_date = data_clean['Datetime'].iloc[-1]
        future_dates = pd.date_range(start=last_date + timedelta(days=1), periods=days_ahead, freq='D')

        return future_dates, future_prices, None
    except Exception as e:
        st.error(f"Exponential smoothing error: {e}")
        return None, None, None

def bollinger_bands_prediction(data, days_ahead=30, window=20, num_std=2):
    """Bollinger Bands based prediction"""
    try:
        data_clean = data.dropna()

        # Calculate Bollinger Bands
        rolling_mean = data_clean['Close'].rolling(window=window).mean()
        rolling_std = data_clean['Close'].rolling(window=window).std()

        upper_band = rolling_mean + (rolling_std * num_std)
        lower_band = rolling_mean - (rolling_std * num_std)

        # Current position relative to bands
        current_price = data_clean['Close'].iloc[-1]
        current_upper = upper_band.iloc[-1]
        current_lower = lower_band.iloc[-1]
        current_mean = rolling_mean.iloc[-1]

        # Predict mean reversion
        future_prices = []
        for i in range(days_ahead):
            # Gradual reversion to mean
            reversion_factor = 0.95 ** i  # Exponential decay
            predicted_price = current_price * reversion_factor + current_mean * (1 - reversion_factor)
            future_prices.append(predicted_price)

        # Create future dates
        last_date = data_clean['Datetime'].iloc[-1]
        future_dates = pd.date_range(start=last_date + timedelta(days=1), periods=days_ahead, freq='D')

        return future_dates, future_prices, None
    except Exception as e:
        st.error(f"Bollinger Bands prediction error: {e}")
        return None, None, None

###############################################
## PART 2: Creating the Dashboard App layout ##
###############################################


# Set up Streamlit page layout
st.set_page_config(layout="wide")
st.title('Real Time Stock Dashboard')


# 2A: SIDEBAR PARAMETERS ############

# Sidebar for user input parameters
st.sidebar.header('Chart Parameters')
ticker = st.sidebar.text_input('Ticker', 'ADBE')
time_period = st.sidebar.selectbox('Time Period', ['1d', '1wk', '1mo', '1y', 'max'])
chart_type = st.sidebar.selectbox('Chart Type', ['Candlestick', 'Line'])
indicators = st.sidebar.multiselect('Technical Indicators', ['SMA 20', 'EMA 20'])

# Prediction Parameters
st.sidebar.header('Prediction Settings')
show_predictions = st.sidebar.checkbox('Show Predictions', value=True)
prediction_days = st.sidebar.slider('Days to Predict', 7, 90, 30)
prediction_models = st.sidebar.multiselect(
    'Prediction Models',
    ['Linear Regression', 'Polynomial Regression', 'Moving Average', 'Exponential Smoothing', 'Bollinger Bands'],
    default=['Linear Regression', 'Moving Average']
)

# Mapping of time periods to data intervals
interval_mapping = {
    '1d': '1m',
    '1wk': '30m',
    '1mo': '1d',
    '1y': '1wk',
    'max': '1wk'
}


# 2B: MAIN CONTENT AREA ############

# Function to display dashboard content
def display_dashboard(ticker, time_period, chart_type, indicators, show_predictions=True, prediction_days=30, prediction_models=[]):
    try:
        data = fetch_stock_data(ticker, time_period, interval_mapping[time_period])
        data = process_data(data)
        data = add_technical_indicators(data)

        last_close, change, pct_change, high, low, volume = calculate_metrics(data)

        # Display main metrics
        st.metric(label=f"{ticker} Last Price", value=f"{last_close:.2f} USD", delta=f"{change:.2f} ({pct_change:.2f}%)")

        col1, col2, col3 = st.columns(3)
        col1.metric("High", f"{high:.2f} USD")
        col2.metric("Low", f"{low:.2f} USD")
        col3.metric("Volume", f"{volume:,}")

        # Plot the stock price chart
        st.subheader(f'{ticker} {time_period.upper()} Chart')

        if chart_type == 'Candlestick':
            fig = go.Figure()
            fig.add_trace(go.Candlestick(
                x=data['Datetime'],
                open=data['Open'],
                high=data['High'],
                low=data['Low'],
                close=data['Close'],
                name=ticker
            ))

            # Add selected technical indicators to the chart
            for indicator in indicators:
                if indicator == 'SMA 20' and 'SMA_20' in data.columns:
                    fig.add_trace(go.Scatter(
                        x=data['Datetime'],
                        y=data['SMA_20'],
                        name='SMA 20',
                        line=dict(color='orange', width=2)
                    ))
                elif indicator == 'EMA 20' and 'EMA_20' in data.columns:
                    fig.add_trace(go.Scatter(
                        x=data['Datetime'],
                        y=data['EMA_20'],
                        name='EMA 20',
                        line=dict(color='purple', width=2)
                    ))

            # Format graph
            fig.update_layout(
                title=f'{ticker} {time_period.upper()} Candlestick Chart',
                xaxis_title='Time',
                yaxis_title='Price (USD)',
                height=600,
                showlegend=True
            )
            st.plotly_chart(fig, use_container_width=True)

        else:
            # Line chart
            fig = go.Figure()
            fig.add_trace(go.Scatter(
                x=data['Datetime'],
                y=data['Close'],
                mode='lines',
                name=f'{ticker} Close Price',
                line=dict(color='blue', width=2)
            ))

            # Add selected technical indicators to the chart
            for indicator in indicators:
                if indicator == 'SMA 20' and 'SMA_20' in data.columns:
                    fig.add_trace(go.Scatter(
                        x=data['Datetime'],
                        y=data['SMA_20'],
                        name='SMA 20',
                        line=dict(color='orange', width=2)
                    ))
                elif indicator == 'EMA 20' and 'EMA_20' in data.columns:
                    fig.add_trace(go.Scatter(
                        x=data['Datetime'],
                        y=data['EMA_20'],
                        name='EMA 20',
                        line=dict(color='purple', width=2)
                    ))

            # Format graph
            fig.update_layout(
                title=f'{ticker} {time_period.upper()} Line Chart',
                xaxis_title='Time',
                yaxis_title='Price (USD)',
                height=600,
                showlegend=True
            )
            st.plotly_chart(fig, use_container_width=True)

        # Fallback: Simple Streamlit line chart
        st.subheader('Simple Price Chart (Fallback)')
        st.line_chart(data.set_index('Datetime')['Close'])

        # PREDICTION CHARTS
        if show_predictions and prediction_models:
            st.header("🔮 Stock Price Predictions")

            # Create prediction charts
            prediction_results = {}

            for model_name in prediction_models:
                if model_name == 'Linear Regression':
                    dates, prices, score = linear_regression_prediction(data, prediction_days)
                elif model_name == 'Polynomial Regression':
                    dates, prices, score = polynomial_regression_prediction(data, prediction_days)
                elif model_name == 'Moving Average':
                    dates, prices, score = moving_average_prediction(data, prediction_days)
                elif model_name == 'Exponential Smoothing':
                    dates, prices, score = exponential_smoothing_prediction(data, prediction_days)
                elif model_name == 'Bollinger Bands':
                    dates, prices, score = bollinger_bands_prediction(data, prediction_days)

                if dates is not None and prices is not None:
                    prediction_results[model_name] = {
                        'dates': dates,
                        'prices': prices,
                        'score': score
                    }

            # Display individual prediction charts
            if prediction_results:
                cols = st.columns(2)
                col_idx = 0

                for model_name, result in prediction_results.items():
                    with cols[col_idx % 2]:
                        st.subheader(f"{model_name} Prediction")

                        # Create individual chart
                        fig = go.Figure()

                        # Historical data
                        fig.add_trace(go.Scatter(
                            x=data['Datetime'][-60:],  # Last 60 days
                            y=data['Close'][-60:],
                            mode='lines',
                            name='Historical',
                            line=dict(color='blue', width=2)
                        ))

                        # Prediction
                        fig.add_trace(go.Scatter(
                            x=result['dates'],
                            y=result['prices'],
                            mode='lines',
                            name=f'{model_name} Prediction',
                            line=dict(color='red', width=2, dash='dash')
                        ))

                        fig.update_layout(
                            title=f'{ticker} - {model_name}',
                            xaxis_title='Date',
                            yaxis_title='Price (USD)',
                            height=400,
                            showlegend=True
                        )

                        st.plotly_chart(fig, use_container_width=True)

                        # Show accuracy if available
                        if result['score'] is not None:
                            st.metric(f"{model_name} R² Score", f"{result['score']:.4f}")

                    col_idx += 1

                # Combined prediction chart
                st.subheader("📊 All Predictions Combined")
                fig_combined = go.Figure()

                # Historical data
                fig_combined.add_trace(go.Scatter(
                    x=data['Datetime'][-60:],
                    y=data['Close'][-60:],
                    mode='lines',
                    name='Historical Price',
                    line=dict(color='blue', width=3)
                ))

                # All predictions
                colors = ['red', 'green', 'orange', 'purple', 'brown']
                for i, (model_name, result) in enumerate(prediction_results.items()):
                    fig_combined.add_trace(go.Scatter(
                        x=result['dates'],
                        y=result['prices'],
                        mode='lines',
                        name=f'{model_name}',
                        line=dict(color=colors[i % len(colors)], width=2, dash='dash')
                    ))

                fig_combined.update_layout(
                    title=f'{ticker} - All Prediction Models Comparison',
                    xaxis_title='Date',
                    yaxis_title='Price (USD)',
                    height=600,
                    showlegend=True
                )

                st.plotly_chart(fig_combined, use_container_width=True)

                # Prediction summary table
                st.subheader("📈 Prediction Summary")
                summary_data = []
                current_price = float(data['Close'].iloc[-1])

                for model_name, result in prediction_results.items():
                    final_price = result['prices'][-1]
                    price_change = final_price - current_price
                    pct_change = (price_change / current_price) * 100

                    summary_data.append({
                        'Model': model_name,
                        'Current Price': f"${current_price:.2f}",
                        f'Predicted Price ({prediction_days}d)': f"${final_price:.2f}",
                        'Price Change': f"${price_change:.2f}",
                        'Percentage Change': f"{pct_change:.2f}%",
                        'R² Score': f"{result['score']:.4f}" if result['score'] is not None else "N/A"
                    })

                summary_df = pd.DataFrame(summary_data)
                st.dataframe(summary_df, use_container_width=True)

        # Display historical data and technical indicators
        st.subheader('Historical Data')
        st.dataframe(data[['Datetime', 'Open', 'High', 'Low', 'Close', 'Volume']])

        st.subheader('Technical Indicators')
        st.dataframe(data[['Datetime', 'SMA_20', 'EMA_20']])

    except Exception as e:
        st.error(f"Error loading data for {ticker}: {str(e)}")
        st.info("Please check the ticker symbol and try again.")

# Always show dashboard content
st.write("## Stock Dashboard")
st.write(f"**Current Settings:** Ticker: {ticker}, Period: {time_period}, Chart: {chart_type}")

# Auto-load dashboard on startup or when Update button is clicked
if st.sidebar.button('Update') or 'initialized' not in st.session_state:
    st.session_state.initialized = True
    display_dashboard(ticker, time_period, chart_type, indicators, show_predictions, prediction_days, prediction_models)
else:
    # Show default content if not initialized
    st.info("👆 Click 'Update' in the sidebar to load stock data, or wait for auto-load...")
    display_dashboard(ticker, time_period, chart_type, indicators, show_predictions, prediction_days, prediction_models)


# 2C: SIDEBAR PRICES ############

# Sidebar section for real-time stock prices of selected symbols
st.sidebar.header('Real-Time Stock Prices')
stock_symbols = ['AAPL', 'GOOGL', 'AMZN', 'MSFT']
for symbol in stock_symbols:
    try:
        real_time_data = fetch_stock_data(symbol, '1d', '1m')
        if not real_time_data.empty:
            real_time_data = process_data(real_time_data)
            last_price = float(real_time_data['Close'].iloc[-1])
            open_price = float(real_time_data['Open'].iloc[0])
            change = last_price - open_price
            pct_change = (change / open_price) * 100
            st.sidebar.metric(f"{symbol}", f"{last_price:.2f} USD", f"{change:.2f} ({pct_change:.2f}%)")
    except Exception as e:
        st.sidebar.error(f"Error loading {symbol}: {str(e)[:50]}...")

# Sidebar information section
st.sidebar.subheader('About')
st.sidebar.info('This dashboard provides stock data and technical indicators for various time periods. Use the sidebar to customize your view.')

