import streamlit as st
import pandas as pd
import yfinance as yf
from datetime import datetime

st.title("Simple Stock Dashboard Test")

st.write("This is a test to make sure Streamlit is working properly.")

# Test basic functionality
ticker = st.selectbox("Select a stock:", ["AAPL", "GOOGL", "MSFT", "ADBE"])

if st.button("Get Stock Data"):
    try:
        # Fetch data
        data = yf.download(ticker, period="5d", interval="1d")
        
        if not data.empty:
            st.success(f"Successfully loaded data for {ticker}")
            
            # Show basic info
            latest_price = data['Close'].iloc[-1]
            st.metric(f"{ticker} Latest Price", f"${latest_price:.2f}")
            
            # Show data
            st.subheader("Recent Data")
            st.dataframe(data.tail())
            
            # Simple line chart
            st.subheader("Price Chart")
            st.line_chart(data['Close'])
        else:
            st.error("No data found")
            
    except Exception as e:
        st.error(f"Error: {str(e)}")

st.write("---")
st.write("If you can see this, Streamlit is working!")
